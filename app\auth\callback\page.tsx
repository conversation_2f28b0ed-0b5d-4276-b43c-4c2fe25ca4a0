"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/auth-provider";

export default function AuthCallbackPage() {
  const router = useRouter();
  const { authenticated, hasRequiredRole, initializing } = useAuth();

  useEffect(() => {
    // Wait for auth initialization to complete
    if (initializing) return;

    // Add a small delay to ensure Keycloak has processed the callback
    const timer = setTimeout(() => {
      if (authenticated && hasRequiredRole) {
        router.replace("/inbox");
      } else if (authenticated && !hasRequiredRole) {
        router.replace("/login?error=no_role");
      } else {
        router.replace("/login?error=auth_failed");
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [authenticated, hasRequiredRole, initializing, router]);

  return (
    <main className="min-h-[calc(100vh-2rem)] flex items-center justify-center p-4">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-muted-foreground">Processing authentication...</p>
      </div>
    </main>
  );
}
