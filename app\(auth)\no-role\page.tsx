"use client";

import { useEffect } from "react";
import Link from "next/link";
import { useAuth } from "@/components/auth-provider";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShieldAlert } from "lucide-react";

export default function NoRolePage() {
  const { authenticated, logout, requiredRole } = useAuth();

  useEffect(() => {
    if (authenticated) {
      // Auto logout immediately when landing here
      logout();
    }
  }, [authenticated, logout]);

  return (
    <main className="min-h-[calc(100vh-2rem)] flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Alert variant="destructive" className="mb-4">
          <ShieldAlert className="h-4 w-4" />
          <AlertTitle>Access denied</AlertTitle>
          <AlertDescription className="space-y-2">
            <p>You do not have the required role to access this application.</p>
            <p>
              Required role: <strong>{requiredRole}</strong>
            </p>
          </AlertDescription>
        </Alert>
        <div className="grid gap-2">
          <Button asChild variant="outline">
            <Link href="/">Back to Home</Link>
          </Button>
        </div>
      </div>
    </main>
  );
}

