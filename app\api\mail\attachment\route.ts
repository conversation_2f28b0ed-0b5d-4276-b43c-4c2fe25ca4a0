import type { NextRequest } from "next/server"
import { getServerEnv } from "@/lib/config"
import { ensureRole, verifyAuth } from "@/lib/auth"
import { withImapClient } from "@/lib/imap"
import type { ImapFlow } from "imapflow"

export async function GET(req: NextRequest) {
  try {
    const auth = await verifyAuth(req)
    const { requiredRole, imap } = getServerEnv()
    ensureRole(auth, requiredRole)

    const { searchParams } = new URL(req.url)
    const uid = Number(searchParams.get("uid"))
    const partId = searchParams.get("partId")
    if (!uid || !partId) {
      return new Response("Missing uid or partId", { status: 400 })
    }

    if (!imap.user || !imap.pass) {
      const headers = new Headers()
      headers.set("Content-Type", "text/plain")
      headers.set("Content-Disposition", `attachment; filename="demo.txt"`)
      const body = "This is a demo attachment."
      return new Response(body, { headers })
    }

    const { stream, contentType, filename, size } = await withImapClient(imap, async (client: ImapFlow) => {
      const download = await client.download(uid, partId, { uid: true })
      const contentType = download.contentType || "application/octet-stream"
      // @ts-ignore size might not be present
      const size = download.size as number | undefined
      const filename = download.filename || `attachment-${uid}-${partId}`
      return { stream: download.content, contentType, filename, size }
    })

    const headers = new Headers()
    headers.set("Content-Type", contentType)
    headers.set("Content-Disposition", `attachment; filename="${filename}"`)
    if (size) headers.set("Content-Length", String(size))

    return new Response(stream as any, { headers })
  } catch (err: any) {
    const status = Number(err?.status) || 500
    const message = typeof err?.message === "string" ? err.message : "Internal Server Error"
    return new Response(message, { status })
  }
}
