import { ImapFlow, type FetchMessageObject } from "imapflow"
import { simpleParser } from "mailparser"

export type ImapConfig = {
  host: string
  port: number
  secure: boolean
  user?: string
  pass?: string
}

export async function withImapClient<T>(cfg: ImapConfig, fn: (client: ImapFlow) => Promise<T>): Promise<T> {
  if (!cfg.user || !cfg.pass) {
    throw new Error("IMAP credentials not configured")
  }
  const client = new ImapFlow({
    host: cfg.host,
    port: cfg.port,
    secure: cfg.secure,
    auth: {
      user: cfg.user!,
      pass: cfg.pass!,
    },
    logger: false,
  })

  try {
    await client.connect()
    await client.mailboxOpen("INBOX", { readOnly: true })
    const res = await fn(client)
    return res
  } finally {
    try {
      await client.logout()
    } catch {
      // ignore
    }
  }
}

export type MailListItem = {
  uid: number
  subject: string
  from: string
  recipients: string[]
  date: string
  seen: boolean
}

export type ListOptions = {
  page: number
  pageSize: number
  recipientFilter?: string
}

export type ListResult = {
  items: MailListItem[]
  total: number
  page: number
  pageSize: number
}

export async function listMessages(client: ImapFlow, opts: ListOptions): Promise<ListResult> {
  const page = Math.max(1, opts.page)
  const pageSize = Math.min(100, Math.max(5, opts.pageSize))
  const q = (opts.recipientFilter || "").trim().toLowerCase()

  // 1) Get all UIDs (ascending), reverse for latest first
  const allUids = await client.search({ all: true }, { uid: true })
  const uidsDesc = allUids.slice().reverse()

  let filteredUids: number[] = uidsDesc

  // 2) If filtering by recipient, scan headers in batches and build filtered uid list
  if (q) {
    filteredUids = []
    const batchSize = 200
    for (let i = 0; i < uidsDesc.length; i += batchSize) {
      const batch = uidsDesc.slice(i, i + batchSize)
      const matches = await filterBatchByRecipient(client, batch, q)
      filteredUids.push(...matches)
    }
  }

  const total = filteredUids.length
  const start = (page - 1) * pageSize
  const pageUids = filteredUids.slice(start, start + pageSize)

  const items: MailListItem[] = await fetchListMetadata(client, pageUids)

  return {
    items,
    total,
    page,
    pageSize,
  }
}

async function filterBatchByRecipient(client: ImapFlow, uids: number[], q: string): Promise<number[]> {
  const result: number[] = []
  const fetcher = client.fetch(uids, { uid: true, envelope: true, flags: true, headers: true })
  for await (const msg of fetcher) {
    const headers = normalizeHeaders(msg)
    const combined: string[] = []

    for (const field of ["to", "cc", "bcc"] as const) {
      const headerVal = headers[field]
      if (headerVal) combined.push(headerVal)
    }
    for (const field of ["delivered-to", "x-forwarded-to"] as const) {
      const headerVal = headers[field]
      if (headerVal) combined.push(headerVal)
    }

    const hay = combined.join(" ").toLowerCase()
    if (hay.includes(q)) {
      result.push(Number(msg.uid))
    }
  }
  return result
}

async function fetchListMetadata(client: ImapFlow, uids: number[]): Promise<MailListItem[]> {
  if (uids.length === 0) return []
  const out: MailListItem[] = []
  const fetcher = client.fetch(uids, { uid: true, envelope: true, flags: true, headers: true })
  for await (const msg of fetcher) {
    const env = msg.envelope!
    const seen = (msg.flags || []).includes("\\Seen")
    const fromStr = addressListToString(env.from)
    const recipients: string[] = []
    if (env.to) recipients.push(...addressesToStrings(env.to))
    if (env.cc) recipients.push(...addressesToStrings(env.cc))
    if (env.bcc) recipients.push(...addressesToStrings(env.bcc))
    const headers = normalizeHeaders(msg)
    if (headers["delivered-to"]) recipients.push(...splitAddresses(headers["delivered-to"]))
    if (headers["x-forwarded-to"]) recipients.push(...splitAddresses(headers["x-forwarded-to"]))

    out.push({
      uid: Number(msg.uid),
      subject: env.subject || "",
      from: fromStr,
      recipients: uniqueStrings(recipients),
      date: env.date ? new Date(env.date).toISOString() : new Date().toISOString(),
      seen,
    })
  }
  // Keep same order as requested uids
  const order = new Map(uids.map((u, i) => [u, i]))
  out.sort((a, b) => order.get(a.uid)! - order.get(b.uid)!)
  return out
}

function normalizeHeaders(msg: FetchMessageObject): Record<string, string> {
  const h: Record<string, string> = {}
  if (msg.headers) {
    for (const [key, val] of msg.headers) {
      h[String(key).toLowerCase()] = Array.isArray(val) ? val.map(String).join(", ") : String(val)
    }
  }
  return h
}

function addressListToString(list?: { name?: string | null; address?: string | null }[] | null): string {
  if (!list || list.length === 0) return ""
  const a = list[0]
  if (!a) return ""
  if (a.name && a.address) return `${a.name} <${a.address}>`
  return a.address || ""
}

function addressesToStrings(list?: { name?: string | null; address?: string | null }[] | null): string[] {
  if (!list) return []
  return list.map((a) => (a?.name && a?.address ? `${a.name} <${a.address}>` : a?.address || "")).filter(Boolean)
}

function splitAddresses(v: string): string[] {
  // These headers can be comma-separated
  return v
    .split(",")
    .map((s) => s.trim())
    .filter(Boolean)
}

function uniqueStrings(arr: string[]): string[] {
  return Array.from(new Set(arr))
}

export type MailDetail = {
  uid: number
  subject: string
  from: string
  to: string[]
  cc: string[]
  bcc: string[]
  deliveredTo: string[]
  xForwardedTo: string[]
  date: string
  html?: string
  text?: string
  attachments: { partId: string; filename: string; contentType: string; size: number }[]
}

export async function getMessageDetail(client: ImapFlow, uid: number): Promise<MailDetail> {
  // Fetch headers + body structure + raw source to parse
  let envelope: FetchMessageObject["envelope"] | undefined
  let headersMap: FetchMessageObject["headers"] | undefined
  let bodyStructure: any
  let raw: Buffer | null = null

  for await (const msg of client.fetch([uid], {
    uid: true,
    envelope: true,
    headers: true,
    bodyStructure: true,
    source: true,
  })) {
    envelope = msg.envelope
    headersMap = msg.headers
    bodyStructure = msg.bodyStructure
    // @ts-ignore - imapflow returns source as Buffer in Next.js
    raw = msg.source as Buffer
  }
  if (!envelope || !headersMap || !bodyStructure) {
    throw new Error("Message not found")
  }

  const headers = normalizeHeaders({ headers: headersMap } as any)
  const parsed = raw ? await simpleParser(raw) : null

  const attachments = collectAttachments(bodyStructure)

  return {
    uid,
    subject: envelope.subject || "",
    from: addressListToString(envelope.from),
    to: addressesToStrings(envelope.to),
    cc: addressesToStrings(envelope.cc),
    bcc: addressesToStrings(envelope.bcc),
    deliveredTo: headers["delivered-to"] ? splitAddresses(headers["delivered-to"]) : [],
    xForwardedTo: headers["x-forwarded-to"] ? splitAddresses(headers["x-forwarded-to"]) : [],
    date: envelope.date ? new Date(envelope.date).toISOString() : new Date().toISOString(),
    html: parsed?.html || undefined,
    text: parsed?.text || undefined,
    attachments,
  }
}

type BodyNode = {
  part: string
  type: string
  subtype: string
  params?: Record<string, string>
  disposition?: string
  dispositionParameters?: Record<string, string>
  childNodes?: BodyNode[]
  size?: number
}

function collectAttachments(node: BodyNode): { partId: string; filename: string; contentType: string; size: number }[] {
  const out: { partId: string; filename: string; contentType: string; size: number }[] = []

  const walk = (n: BodyNode) => {
    const contentType = `${(n.type || "").toLowerCase()}/${(n.subtype || "").toLowerCase()}`
    const filename = n.dispositionParameters?.filename || n.params?.name
    const isAttachment = (n.disposition || "").toLowerCase() === "attachment" || Boolean(filename)
    if (isAttachment && n.part) {
      out.push({
        partId: n.part,
        filename: filename || "",
        contentType,
        size: n.size || 0,
      })
    }
    if (Array.isArray(n.childNodes)) {
      for (const c of n.childNodes) walk(c)
    }
  }
  walk(node)
  return out
}
