"use client";

import type React from "react";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { Mail, Settings, LogOut, Menu } from "lucide-react";
import { useAuth } from "@/components/auth-provider";

type NavItem = {
  label: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
};

type UserInfo = {
  name?: string | null;
  email?: string | null;
};

const navItems: NavItem[] = [
  { label: "Inbox", href: "/inbox", icon: Mail },
  { label: "Settings", href: "/inbox?tab=settings", icon: Settings },
];

export default function AppShell({
  children,
  pageTitle = "Inbox",
}: {
  children: React.ReactNode;
  pageTitle?: string;
}) {
  const pathname = usePathname();
  const { logout, profile } = useAuth();
  const [open, setOpen] = useState(false);

  const user: UserInfo = {
    name: (profile as any)?.firstName || (profile as any)?.username || null,
    email: (profile as any)?.email || null,
  };

  const SidebarContent = (
    <div className="flex h-full flex-col">
      <div className="flex items-center gap-2 p-3">
        <Mail className="h-6 w-6 text-emerald-600" />
        <div className="font-semibold">Mail Aggregator</div>
      </div>
      <Separator />
      <nav className="flex-1 p-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          const active =
            item.href === "/inbox"
              ? pathname.startsWith("/inbox")
              : pathname === item.href;
          return (
            <Link
              key={item.href}
              href={item.href}
              className="block"
            >
              <Button
                variant={active ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start gap-2",
                  active && "font-medium"
                )}
                onClick={() => setOpen(false)}
              >
                <Icon className="h-4 w-4" />
                <span className="truncate">{item.label}</span>
              </Button>
            </Link>
          );
        })}
      </nav>
      <div className="p-2 text-xs text-muted-foreground px-3">
        {user.name || user.email ? (
          <div className="mb-2 truncate">
            <div className="font-medium truncate">{user.name || "User"}</div>
            {user.email && <div className="truncate">{user.email}</div>}
          </div>
        ) : null}
        <Button
          variant="outline"
          className="w-full justify-start gap-2 bg-transparent"
          onClick={() => logout()}
        >
          <LogOut className="h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen w-full bg-background text-foreground">
      <div className="flex">
        {/* Desktop sidebar */}
        <aside className="hidden md:block w-64 shrink-0 border-r bg-card">
          {SidebarContent}
        </aside>

        {/* Content area */}
        <div className="min-h-screen flex-1">
          {/* Mobile top bar */}
          <header className="md:hidden sticky top-0 z-30 w-full border-b bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <Sheet
                  open={open}
                  onOpenChange={setOpen}
                >
                  <SheetTrigger asChild>
                    <Button
                      size="icon"
                      variant="ghost"
                      aria-label="Open menu"
                    >
                      <Menu className="h-5 w-5" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent
                    side="left"
                    className="p-0 w-72"
                  >
                    <SheetHeader className="sr-only">
                      <SheetTitle>Menu</SheetTitle>
                    </SheetHeader>
                    {SidebarContent}
                  </SheetContent>
                </Sheet>
                <div className="font-semibold">{pageTitle}</div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-transparent"
                onClick={() => logout()}
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            </div>
          </header>

          {/* Page content */}
          <main className="p-3 md:p-6">{children}</main>
        </div>
      </div>
    </div>
  );
}
