"use client";

import Keycloak, {
  type KeycloakConfig,
  type KeycloakProfile,
} from "keycloak-js";
import type React from "react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

type AuthContextType = {
  keycloak: Keycloak | null;
  token: string | null;
  idToken: string | null;
  profile: KeycloakProfile | null;
  authenticated: boolean;
  initializing: boolean;
  hasRequiredRole: boolean;
  requiredRole: string;
  configOk: boolean;
  login: () => void;
  logout: () => void;
  getAuthHeader: () => Promise<string | null>;
};

const AuthContext = createContext<AuthContextType | null>(null);

const PUBLIC_URL = process.env.NEXT_PUBLIC_KEYCLOAK_URL;
const PUBLIC_REALM = process.env.NEXT_PUBLIC_KEYCLOAK_REALM;
const PUBLIC_CLIENT_ID = process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID;
const REQUIRED_ROLE = process.env.NEXT_PUBLIC_REQUIRED_ROLE || "one-mail-admin";

function createKeycloak(): Keycloak | null {
  if (!PUBLIC_URL || !PUBLIC_REALM || !PUBLIC_CLIENT_ID) return null;
  const config: KeycloakConfig = {
    url: PUBLIC_URL,
    realm: PUBLIC_REALM,
    clientId: PUBLIC_CLIENT_ID,
  };
  return new Keycloak(config);
}

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [keycloak, setKeycloak] = useState<Keycloak | null>(null);
  const [authenticated, setAuthenticated] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const [token, setToken] = useState<string | null>(null);
  const [idToken, setIdToken] = useState<string | null>(null);
  const [profile, setProfile] = useState<KeycloakProfile | null>(null);

  const refreshTimer = useRef<number | null>(null);

  const configOk = Boolean(PUBLIC_URL && PUBLIC_REALM && PUBLIC_CLIENT_ID);

  useEffect(() => {
    const kc = createKeycloak();
    setKeycloak(kc);
    if (!kc) {
      setInitializing(false);
      return;
    }

    kc.init({
      pkceMethod: "S256",
      checkLoginIframe: false,
      enableLogging: true,
    })
      .then(async (auth) => {
        setAuthenticated(auth);
        if (auth) {
          setToken(kc.token ?? null);
          setIdToken(kc.idToken ?? null);
          // Skip calling Keycloak account profile endpoint to avoid 403 on /realms/{realm}/account
          // setProfile(null);
          scheduleRefresh(kc);
        }
      })
      .finally(() => setInitializing(false));

    return () => {
      if (refreshTimer.current) window.clearInterval(refreshTimer.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const scheduleRefresh = (kc: Keycloak) => {
    if (refreshTimer.current) window.clearInterval(refreshTimer.current);
    refreshTimer.current = window.setInterval(async () => {
      try {
        const refreshed = await kc.updateToken(30);
        if (refreshed) {
          setToken(kc.token ?? null);
          setIdToken(kc.idToken ?? null);
        }
      } catch {
        // token refresh failed
      }
    }, 30_000);
  };

  const login = useCallback(() => {
    keycloak?.login({
      redirectUri: `${window.location.origin}/auth/callback`,
    });
  }, [keycloak]);

  const logout = useCallback(() => {
    if (!keycloak) return;
    keycloak.logout({ redirectUri: `${window.location.origin}/` });
  }, [keycloak]);

  const roles = useMemo(() => {
    // Keycloak stores roles inside tokenParsed -> realm_access / resource_access
    const realmRoles = ((keycloak?.tokenParsed as any)?.realm_access?.roles ??
      []) as string[];
    const clientId = PUBLIC_CLIENT_ID || "";
    const clientRoles: string[] =
      (keycloak?.tokenParsed as any)?.resource_access?.[clientId]?.roles ?? [];

    const allRoles = new Set<string>([...realmRoles, ...clientRoles]);

    // Debug logging
    console.log("=== Role Debug Info ===");
    console.log("Realm roles:", realmRoles);
    console.log("Client ID:", clientId);
    console.log("Client roles:", clientRoles);
    console.log("All roles:", Array.from(allRoles));
    console.log("Required role:", REQUIRED_ROLE);
    console.log("Has required role:", allRoles.has(REQUIRED_ROLE));

    // Print full JWT token content
    if (keycloak?.token) {
      try {
        const tokenParts = keycloak.token.split(".");
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          console.log("=== Full JWT Access Token Payload ===");
          console.log(JSON.stringify(payload, null, 2));
          console.log("=====================================");
        }
      } catch (error) {
        console.error("Failed to parse JWT access token:", error);
      }
    }

    // Print ID token content if available
    if (keycloak?.idToken) {
      try {
        const tokenParts = keycloak.idToken.split(".");
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          console.log("=== Full JWT ID Token Payload ===");
          console.log(JSON.stringify(payload, null, 2));
          console.log("=================================");
        }
      } catch (error) {
        console.error("Failed to parse JWT ID token:", error);
      }
    }

    // Print full keycloak object for debugging
    console.log("=== Full Keycloak Object Debug ===");
    console.log("keycloak.tokenParsed:", keycloak?.tokenParsed);
    console.log("keycloak.idTokenParsed:", keycloak?.idTokenParsed);
    console.log("keycloak.authenticated:", (keycloak as any)?.authenticated);
    console.log("keycloak.subject:", (keycloak as any)?.subject);
    console.log("==================================");

    return allRoles;
  }, [keycloak, keycloak?.token, keycloak?.idToken]);

  const hasRequiredRole = roles.has(REQUIRED_ROLE);

  const getAuthHeader = useCallback(async () => {
    if (!keycloak) return null;
    try {
      await keycloak.updateToken(30);
      return keycloak.token ? `Bearer ${keycloak.token}` : null;
    } catch {
      return token ? `Bearer ${token}` : null;
    }
  }, [keycloak, token]);

  const value: AuthContextType = {
    keycloak,
    token,
    idToken,
    profile,
    authenticated,
    initializing,
    hasRequiredRole,
    requiredRole: REQUIRED_ROLE,
    configOk,
    login,
    logout,
    getAuthHeader,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) {
    throw new Error("useAuth must be used within AuthProvider");
  }
  return ctx;
}
