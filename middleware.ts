import { NextRequest, NextResponse } from "next/server"
import { verifyAuth, ensureRole } from "@/lib/auth"
import { getServerEnv } from "@/lib/config"

export async function middleware(request: NextRequest) {
  const requestHeaders = new Headers(request.headers)

  const forwardedHost =
    request.headers.get("x-forwarded-host") ||
    request.headers.get("x-original-host") ||
    request.headers.get("host")

  const forwardedProto =
    request.headers.get("x-forwarded-proto") ||
    request.headers.get("x-forwarded-protocol") ||
    (request.headers.get("x-forwarded-ssl") === "on" ? "https" : "http")

  if (forwardedHost && forwardedHost !== "localhost:9042") {
    requestHeaders.set("host", forwardedHost)
  }
  if (forwardedProto) {
    requestHeaders.set("x-forwarded-proto", forwardedProto)
  }

  // Allow CORS preflight without auth
  if (request.method === "OPTIONS") {
    return NextResponse.next({
      request: { headers: requestHeaders },
    })
  }

  // Enforce JWT + role check for API routes
  try {
    const auth = await verifyAuth(request)
    const { requiredRole } = getServerEnv()
    ensureRole(auth, requiredRole)
  } catch (err: any) {
    const status = Number(err?.status) || 401
    const message = typeof err?.message === "string" ? err.message : "Unauthorized"
    return new Response(message, { status })
  }

  return NextResponse.next({
    request: { headers: requestHeaders },
  })
}

export const config = {
  // Only apply middleware to API routes
  matcher: ["/api/:path*"],
}

